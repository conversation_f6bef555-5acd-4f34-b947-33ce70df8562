/**
 * @file csharp_print.c
 * @brief Implementation of C# print library interop functions
 */

#include "csharp_print.h"
#include <stdio.h>
#include <string.h>

#ifdef _WIN32
#include <windows.h>
#else
#include <dlfcn.h>
#endif

/* Function pointers for C# DLL functions */
static int (*csharp_print_init_func)(void) = NULL;
static void (*csharp_print_cleanup_func)(void) = NULL;
static void (*csharp_print_func)(const char*) = NULL;
static void (*csharp_print_with_instance_func)(EipUint32, const char*) = NULL;
static void (*csharp_print_assembly_data_func)(EipUint32, const char*, const void*, int) = NULL;
static void (*csharp_print_error_func)(int, const char*) = NULL;

/* DLL handle */
#ifdef _WIN32
static HMODULE csharp_dll_handle = NULL;
#else
static void* csharp_dll_handle = NULL;
#endif

/* Flag to track if library is loaded */
static int csharp_library_loaded = 0;

/**
 * @brief Load the C# DLL and get function pointers
 * @return 0 on success, -1 on error
 */
static int LoadCSharpLibrary(void) {
    if (csharp_library_loaded) {
        return 0; /* Already loaded */
    }

#ifdef _WIN32
    /* Windows implementation */
    csharp_dll_handle = LoadLibrary(TEXT("CSharpPrintLibrary.dll"));
    if (csharp_dll_handle == NULL) {
        printf("Failed to load CSharpPrintLibrary.dll\n");
        return -1;
    }

    csharp_print_init_func = (int(*)(void))GetProcAddress(csharp_dll_handle, "CSharpPrintInit");
    csharp_print_cleanup_func = (void(*)(void))GetProcAddress(csharp_dll_handle, "CSharpPrintCleanup");
    csharp_print_func = (void(*)(const char*))GetProcAddress(csharp_dll_handle, "CSharpPrint");
    csharp_print_with_instance_func = (void(*)(EipUint32, const char*))GetProcAddress(csharp_dll_handle, "CSharpPrintWithInstance");
    csharp_print_assembly_data_func = (void(*)(EipUint32, const char*, const void*, int))GetProcAddress(csharp_dll_handle, "CSharpPrintAssemblyData");
    csharp_print_error_func = (void(*)(int, const char*))GetProcAddress(csharp_dll_handle, "CSharpPrintError");
#else
    /* Linux/POSIX implementation */
    csharp_dll_handle = dlopen("./libCSharpPrintLibrary.so", RTLD_LAZY);
    if (!csharp_dll_handle) {
        printf("Failed to load libCSharpPrintLibrary.so: %s\n", dlerror());
        return -1;
    }

    csharp_print_init_func = (int(*)(void))dlsym(csharp_dll_handle, "CSharpPrintInit");
    csharp_print_cleanup_func = (void(*)(void))dlsym(csharp_dll_handle, "CSharpPrintCleanup");
    csharp_print_func = (void(*)(const char*))dlsym(csharp_dll_handle, "CSharpPrint");
    csharp_print_with_instance_func = (void(*)(EipUint32, const char*))dlsym(csharp_dll_handle, "CSharpPrintWithInstance");
    csharp_print_assembly_data_func = (void(*)(EipUint32, const char*, const void*, int))dlsym(csharp_dll_handle, "CSharpPrintAssemblyData");
    csharp_print_error_func = (void(*)(int, const char*))dlsym(csharp_dll_handle, "CSharpPrintError");
#endif

    /* Check if all functions were loaded successfully */
    if (!csharp_print_init_func || !csharp_print_cleanup_func || 
        !csharp_print_func || !csharp_print_with_instance_func ||
        !csharp_print_assembly_data_func || !csharp_print_error_func) {
        printf("Failed to load one or more C# functions\n");
        return -1;
    }

    csharp_library_loaded = 1;
    return 0;
}

int CSharpPrintInit(void) {
    if (LoadCSharpLibrary() != 0) {
        return -1;
    }
    
    if (csharp_print_init_func) {
        return csharp_print_init_func();
    }
    return -1;
}

void CSharpPrintCleanup(void) {
    if (csharp_print_cleanup_func) {
        csharp_print_cleanup_func();
    }

    /* Unload the DLL */
    if (csharp_dll_handle) {
#ifdef _WIN32
        FreeLibrary(csharp_dll_handle);
#else
        dlclose(csharp_dll_handle);
#endif
        csharp_dll_handle = NULL;
    }
    
    csharp_library_loaded = 0;
}

void CSharpPrint(const char* message) {
    if (!csharp_library_loaded && LoadCSharpLibrary() != 0) {
        printf("[Fallback] %s\n", message ? message : "NULL");
        return;
    }
    
    if (csharp_print_func && message) {
        csharp_print_func(message);
    } else {
        printf("[Fallback] %s\n", message ? message : "NULL");
    }
}

void CSharpPrintWithInstance(EipUint32 instance_number, const char* message) {
    if (!csharp_library_loaded && LoadCSharpLibrary() != 0) {
        printf("[Fallback] Instance %u: %s\n", instance_number, message ? message : "NULL");
        return;
    }
    
    if (csharp_print_with_instance_func && message) {
        csharp_print_with_instance_func(instance_number, message);
    } else {
        printf("[Fallback] Instance %u: %s\n", instance_number, message ? message : "NULL");
    }
}

void CSharpPrintAssemblyData(EipUint32 instance_number, 
                            const char* assembly_type,
                            const void* data_ptr, 
                            int data_size) {
    if (!csharp_library_loaded && LoadCSharpLibrary() != 0) {
        printf("[Fallback] Instance %u, Type: %s, Data: %p, Size: %d\n", 
               instance_number, assembly_type ? assembly_type : "NULL", data_ptr, data_size);
        return;
    }
    
    if (csharp_print_assembly_data_func) {
        csharp_print_assembly_data_func(instance_number, assembly_type, data_ptr, data_size);
    } else {
        printf("[Fallback] Instance %u, Type: %s, Data: %p, Size: %d\n", 
               instance_number, assembly_type ? assembly_type : "NULL", data_ptr, data_size);
    }
}

void CSharpPrintError(int error_code, const char* message) {
    if (!csharp_library_loaded && LoadCSharpLibrary() != 0) {
        printf("[Fallback Error] Code %d: %s\n", error_code, message ? message : "NULL");
        return;
    }
    
    if (csharp_print_error_func && message) {
        csharp_print_error_func(error_code, message);
    } else {
        printf("[Fallback Error] Code %d: %s\n", error_code, message ? message : "NULL");
    }
}

void PrintCipInstanceInfo(const CipInstance* instance, const char* message) {
    if (!instance) {
        CSharpPrintError(-1, "NULL CipInstance pointer");
        return;
    }
    
    char buffer[256];
    snprintf(buffer, sizeof(buffer), "%s (Instance Number: %u)", 
             message ? message : "CipInstance Info", instance->instance_number);
    
    CSharpPrintWithInstance(instance->instance_number, buffer);
}

void PrintEipStatus(EipStatus status, const char* context) {
    char buffer[256];
    const char* status_str;
    
    switch (status) {
        case kEipStatusOk:
            status_str = "OK";
            break;
        case kEipStatusOkSend:
            status_str = "OK_SEND";
            break;
        case kEipStatusError:
            status_str = "ERROR";
            break;
        default:
            status_str = "UNKNOWN";
            break;
    }
    
    snprintf(buffer, sizeof(buffer), "%s - Status: %s (%d)", 
             context ? context : "EipStatus", status_str, (int)status);
    
    if (status == kEipStatusError) {
        CSharpPrintError((int)status, buffer);
    } else {
        CSharpPrint(buffer);
    }
}
